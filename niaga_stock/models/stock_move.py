# -*- coding: utf-8 -*-
# Copyright 2022 Altech Omega Andalan PT. - Imam <PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

import json
from collections import defaultdict

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.tools.float_utils import float_compare, float_is_zero, float_round

import odoo.addons.decimal_precision as dp

PRICE_STATUS = [
    ('rising_price', 'Rising Price'),
    ('fixed_price', 'Fixed Price'),
    ('prices_go_down', 'Price Go Down'),
]


class StockMove(models.Model):
    _inherit = 'stock.move'

    @api.depends(
        'discount_amount',
        'discount_first',
        'discount_second',
        'discount_third',
        'price_unit',
        'product_uom_qty',
        'quantity_done',
        'taxes_id',
        'picking_id',
        'picking_id.discount_method',
        'picking_id.discount_amount',
    )
    def _compute_amount(self):
        precision = self.env['decimal.precision'].precision_get('Product Price')
        for line in self:
            product_qty = (
                line.product_uom_qty
                if line.state in ['draft', 'cancel']
                else line.quantity_done
            )
            price_unit = line.price_unit
            discounted_price_unit = line._get_discounted_price_unit()
            if discounted_price_unit != line.price_unit:
                line.price_discount = line.price_unit - discounted_price_unit
                price_unit = float_round(
                    discounted_price_unit, precision_digits=precision
                )

            picking = line.picking_id
            rounding_method = picking.company_id.tax_calculation_rounding_method
            taxes = line.taxes_id.compute_all(
                price_unit,
                line.currency_id,
                product_qty,
                product=line.product_id,
                partner=picking.partner_id,
            )

            price_tax = taxes['total_included'] - taxes['total_excluded']
            if rounding_method == 'round_globally':
                price_tax = sum(t.get('amount', 0.0) for t in taxes.get('taxes', []))

            line.price_tax = price_tax
            line.price_total = taxes['total_included']
            line.price_subtotal = taxes['total_excluded']

            if line.price_subtotal == 0.0 or picking.discount_amount <= 0.0:
                continue

            if picking.discount_method == 'percent':
                price_unit *= 1 - picking.discount_amount / 100
            elif picking.discount_method == 'fixed':
                price_unit -= (price_unit / line.price_subtotal) * (
                    picking.discount_amount / len(picking.move_lines)
                )

            taxes = line.taxes_id.compute_all(
                price_unit,
                line.currency_id,
                product_qty,
                product=line.product_id,
                partner=line.partner_id or line.picking_id.partner_id,
            )

            price_tax = taxes['total_included'] - taxes['total_excluded']
            if rounding_method == 'round_globally':
                price_tax = sum(t.get('amount', 0.0) for t in taxes.get('taxes', []))

            line.price_tax = price_tax

    @api.multi
    def _compute_tax_id(self):
        for line in self:
            fpos = (
                line.picking_id.purchase_id.fiscal_position_id
                or line.picking_id.partner_id.property_account_position_id
            )
            taxes = line.product_id.supplier_taxes_id.filtered(
                lambda r: not line.picking_id.company_id
                or r.company_id == line.picking_id.company_id
            )
            line.taxes_id = (
                fpos.map_tax(taxes, line.product_id, line.picking_id.partner_id)
                if fpos
                else taxes
            )

    move_id = fields.Many2one('stock.move', string='Stock Move', index=True)
    currency_id = fields.Many2one(
        'res.currency', string='Currency', compute='_compute_currency_id', store=True
    )
    price_unit = fields.Float(
        string='Unit Price',
        digits=dp.get_precision('Product Price'),
        copy=True,
    )
    domain_taxes = fields.Char(
        string='Domain for Taxes',
        compute='_compute_domain_taxes',
    )
    taxes_id = fields.Many2many(
        'account.tax',
        string='Taxes',
        domain=['|', ('active', '=', False), ('active', '=', True)],
        copy=True,
    )
    price_subtotal = fields.Monetary(
        compute='_compute_amount',
        string='Subtotal',
        readonly=True,
        store=True,
    )
    price_total = fields.Monetary(compute='_compute_amount', string='Total', store=True)
    price_tax = fields.Monetary(compute='_compute_amount', string='Tax', store=True)
    operating_unit_id = fields.Many2one(
        'operating.unit',
        string='Operating Unit',
        related=False,
        index=True,
    )
    discount_first = fields.Float(
        string='Disc 1 (%)',
        digits=dp.get_precision('Discount'),
        copy=True,
    )
    discount_second = fields.Float(
        string='Disc 2 (%)',
        digits=dp.get_precision('Discount'),
        copy=True,
    )
    discount_third = fields.Float(
        string='Disc 3 (%)',
        digits=dp.get_precision('Discount'),
        copy=True,
    )
    discount_amount = fields.Monetary(
        string='Disc Amount',
        currency_field='currency_id',
        digits=dp.get_precision('Discount'),
        copy=True,
    )
    price_discount = fields.Float(
        compute='_compute_amount',
        string='Discount',
        digits=dp.get_precision('Discount'),
        store=True,
    )
    price_status = fields.Selection(selection=PRICE_STATUS, string='Price Status')
    average_price = fields.Monetary(string='Current Avg Cost')
    qty_returnable = fields.Float(compute=None)
    position_id = fields.Many2one('stock.position', string='Stock Position')
    uom_id = fields.Many2one(related='product_id.uom_id', readonly=True)
    uom_po_id = fields.Many2one(related='product_id.uom_po_id', readonly=True)
    categ_id = fields.Many2one(
        related='product_id.categ_id', store=True, index=True, readonly=True
    )
    is_manual_return = fields.Boolean(
        string='Manual Return',
        Default=False,
        copy=False,
        help='Is this move created from manual return process?',
    )

    @api.depends('state', 'picking_id')
    def _compute_is_initial_demand_editable(self):
        for move in self:
            if self._context.get('planned_picking'):
                move.is_initial_demand_editable = True
            elif move.state != 'done' and move.picking_id:
                move.is_initial_demand_editable = True
            else:
                move.is_initial_demand_editable = False

    @api.depends(
        'move_line_ids.qty_done',
        'move_line_ids.product_uom_id',
        'move_line_nosuggest_ids.qty_done',
    )
    def _quantity_done_compute(self):
        for move in self:
            move_line_ids = move._get_move_lines()
            if not move_line_ids:
                continue

            move.quantity_done = sum(
                ml.product_uom_id._compute_quantity(
                    ml.qty_done, move.product_uom, round=False
                )
                if ml.product_uom_id != move.product_uom
                else ml.qty_done
                for ml in move_line_ids
            )

    @api.depends('picking_id')
    def _compute_currency_id(self):
        for move in self:
            if move.picking_id:
                move.currency_id = move.picking_id.currency_id.id

    @api.one
    @api.depends('picking_id')
    def _compute_domain_taxes(self):
        domain = []
        if self.picking_id.purchase_id:
            domain = [('type_tax_use', '=', 'purchase')]
        if self.picking_id.sale_id:
            domain = [('type_tax_use', '=', 'sale')]
        self.domain_taxes = json.dumps(domain)

    @api.multi
    @api.constrains('picking_id', 'location_id', 'location_dest_id')
    def _check_stock_move_operating_unit(self):
        return super()._check_stock_move_operating_unit()

    @api.multi
    def action_show_details(self):
        self.ensure_one()
        result = super().action_show_details()
        owner_id = (
            self.picking_id.owner_id.id or self.picking_partner_id.id
            if self.picking_type_id.code == 'incoming'
            or self.picking_type_id.is_vendor_return
            else False
        )
        result['context'] = dict(result['context'], owner_id=owner_id)
        return result

    def _onchange_transit(self):
        price_unit = self.product_id.get_product_cost(self.operating_unit_id)
        if self.product_id.uom_id != self.product_uom:
            price_unit = self.product_id.uom_id._compute_price(
                price_unit, self.product_uom
            )
        self.price_unit = price_unit

    @api.onchange('product_id')
    def onchange_product_id(self):
        product = self.product_id.with_context(
            lang=self.partner_id.lang or self.env.user.lang
        )
        self.name = product.partner_ref
        self.product_uom = product.uom_id.id

        if not self.product_id or (
            self.picking_code != 'incoming' or self.picking_id.is_vendor_return
        ):
            return

        if self._is_in_transit() or self._is_out_transit():
            self._onchange_transit()

        self.price_unit = 0.0
        self.taxes_id = self.product_id.supplier_taxes_id
        self.product_uom_qty = 1.0
        self._onchange_quantity()

    @api.onchange('product_uom_qty', 'product_uom', 'purchase_id', 'picking_code')
    def _onchange_quantity(self):
        if not self.product_id or (
            self.picking_code != 'incoming' or self.picking_id.is_vendor_return
        ):
            return

        if self._is_in_transit() or self._is_out_transit():
            self._onchange_transit()

        self = self.with_context(operating_unit_id=self.operating_unit_id.id)
        seller_id = self.product_id._select_seller(
            partner_id=self.picking_id.partner_id
        )

        if not seller_id:
            return

        price_unit = self.env['account.tax']._fix_tax_included_price_company(
            seller_id.price,
            self.product_id.supplier_taxes_id,
            self.taxes_id,
            self.company_id,
        )
        if seller_id.currency_id != self.currency_id:
            price_unit = seller_id.currency_id.compute(price_unit, self.currency_id)

        if seller_id.product_uom != self.product_uom:
            price_unit = seller_id.product_uom._compute_price(
                price_unit, self.product_uom
            )

        self.price_unit = price_unit
        self.discount_first = seller_id.discount_first
        self.discount_second = seller_id.discount_second
        self.discount_third = seller_id.discount_third
        self.discount_amount = seller_id.discount_amount

    def _get_discounted_price_unit(self):
        self.ensure_one()
        discount_fields = ['discount_first', 'discount_second', 'discount_third']
        price_unit = self.price_unit

        for field in discount_fields:
            discount = getattr(self, field)
            if discount:
                price_unit *= 1 - discount / 100

        if self.discount_amount:
            price_unit -= self.discount_amount
        return price_unit

    @api.model
    def create(self, vals):
        if 'operating_unit_id' not in vals:
            if 'picking_id' in vals:
                vals['operating_unit_id'] = (
                    self.env['stock.picking']
                    .browse(vals['picking_id'])
                    .operating_unit_id.id
                )
            elif 'inventory_id' in vals:
                vals['operating_unit_id'] = (
                    self.env['stock.inventory']
                    .browse(vals['inventory_id'])
                    .operating_unit_id.id
                )

        res = super().create(vals)

        if (
            'state' in vals
            or 'returned_move_ids' in vals
            or 'quantity_done' in vals
            or 'reserved_availability' in vals
        ):
            res._update_qty_returnable()
        return res

    def write(self, vals):
        res = super().write(vals)
        if self and (
            'state' in vals
            or 'returned_move_ids' in vals
            or 'quantity_done' in vals
            or 'reserved_availability' in vals
        ):
            self._update_qty_returnable()
        return res

    @api.multi
    def _update_qty_returnable(self):
        for move in self:
            if move.state in ['draft', 'cancel']:
                continue

            if not move.returned_move_ids:
                if move.state == 'done':
                    move.qty_returnable = move.quantity_done
                else:
                    move.qty_returnable = move.reserved_availability
                continue

            move.returned_move_ids._update_qty_returnable()
            return_qty_returnable = 0.0

            for move_return in move.returned_move_ids.filtered(
                lambda x: x.state not in ['draft', 'cancel']
            ):
                if move_return.product_uom != move.product_uom:
                    return_qty_returnable += move_return.product_uom._compute_quantity(
                        move_return.qty_returnable, move.product_uom, round=False
                    )
                else:
                    return_qty_returnable += move_return.qty_returnable

            move.qty_returnable = move.quantity_done - return_qty_returnable

    def _prepare_move_split_vals(self, qty):
        res = super()._prepare_move_split_vals(qty)
        res['operating_unit_id'] = self.operating_unit_id.id
        return res

    def _get_price_unit(self):
        return (
            not self.company_id.currency_id.is_zero(self.price_unit)
            and self.price_unit
            or self.product_id.get_product_cost(self.operating_unit_id)
        )

    @api.model
    def _get_in_base_domain(self, company_id=False):
        domain = super()._get_in_base_domain(company_id)
        operating_unit_id = self.operating_unit_id.id or self._context.get(
            'operating_unit_id', False
        )
        move_date = self.date or self._context.get('date')

        if operating_unit_id:
            domain = [('operating_unit_id', '=', operating_unit_id)] + domain
        if move_date:
            domain = [('date', '<', move_date)] + domain
        return domain

    @api.multi
    def _set_vendor_pricelist(self):
        self.ensure_one()
        ctx = {'operating_unit_id': self.operating_unit_id.id, 'no_sync': True}
        self = self.with_context(ctx)

        if self.price_unit == 0.0 or self.picking_id.sale_id:
            return False

        seller_id = self.product_id._select_seller(
            partner_id=self.picking_id.partner_id
        )
        values = {}
        price_unit = self.price_unit

        if self.product_uom != self.product_id.uom_po_id:
            price_unit = self.product_uom._compute_price(
                price_unit, self.product_id.uom_po_id
            )

        if seller_id:
            discount_fields = [
                'discount_first',
                'discount_second',
                'discount_third',
                'discount_amount',
            ]
            for field in discount_fields:
                if getattr(seller_id, field) != getattr(self, field):
                    values[field] = getattr(self, field)

            if seller_id.price != price_unit:
                values['price'] = price_unit
            if values:
                seller_id.write(values)
        else:
            seller_sequence = (
                max(self.product_id.seller_ids.mapped('sequence')) + 1
                if self.product_id.seller_ids
                else 1
            )
            values.update(
                {
                    'name': self.picking_id.partner_id.id,
                    'sequence': seller_sequence,
                    'min_qty': 0.0,
                    'price': price_unit,
                    'currency_id': self.currency_id.id,
                    'delay': 0,
                    'discount_first': self.discount_first,
                    'discount_second': self.discount_second,
                    'discount_third': self.discount_third,
                    'discount_amount': self.discount_amount,
                    'operating_unit_id': self.operating_unit_id.id,
                }
            )
            self.product_id.seller_ids = [(0, 0, values)]

    @api.multi
    def _set_product_cost(self):
        self.ensure_one()
        self = self.with_context(no_sync=True)
        if self.price_total == 0.0:
            return False

        standard_price = (
            self.product_uom._compute_price(self.price_total, self.product_id.uom_id)
            / self.quantity_done
        )
        operating_unit_id = self._context.get(
            'operating_unit_id', self.operating_unit_id.id
        )

        product_cost = self.product_id.product_cost_ids.filtered(
            lambda p: p.operating_unit_id.id == operating_unit_id
        )
        if product_cost:
            price_change = False
            if (
                float_compare(
                    product_cost.standard_price,
                    standard_price,
                    precision_rounding=self.currency_id.rounding,
                )
                != 0.0
            ):
                price_change = self.env['stock.price.change']._create_from_move(
                    self, product_cost.standard_price, standard_price
                )

            product_cost.write({'standard_price': standard_price})

            if price_change:
                price_change._update_sale_price_new(operating_unit_id)
        else:
            product_cost = self.product_id.product_cost_ids.create(
                {
                    'currency_id': self.currency_id.id,
                    'product_tmpl_id': self.product_id.product_tmpl_id.id,
                    'operating_unit_id': operating_unit_id,
                    'standard_price': standard_price,
                }
            )
            price_change = self.env['stock.price.change']._create_from_move(
                self, 0, standard_price
            )
            if price_change:
                price_change._update_sale_price_new(operating_unit_id)

        return product_cost

    def _is_in(self):
        for move_line in self.move_line_ids:
            if (
                not move_line.location_id._should_be_valued()
                and move_line.location_dest_id._should_be_valued()
            ):
                return True
        return False

    def _is_out(self):
        for move_line in self.move_line_ids:
            if (
                move_line.location_id._should_be_valued()
                and not move_line.location_dest_id._should_be_valued()
            ):
                return True
        return False

    def product_price_update_before_done(self, forced_qty=None):
        move_ids = self.filtered(
            lambda m: m._is_in() and m.location_id.usage in ['supplier', 'production']
        )
        move_ids = move_ids.filtered(lambda m: m.product_id.cost_method == 'average')
        tmpl_dict = defaultdict(lambda: 0.0)
        avg_price_update = {}
        # update average price on incomming moves if the product cost_method is average
        for move in move_ids:
            product_tot_qty_available = (
                move.product_id.with_context(
                    warehouse=move.picking_id.picking_type_id.warehouse_id.id
                ).get_qty_available()
                + tmpl_dict[move.product_id.id]
            )
            rounding = move.product_id.uom_id.rounding

            qty_done = move.quantity_done
            if move.product_uom != move.product_id.uom_id:
                qty_done = move.product_uom._compute_quantity(
                    qty_done, move.product_id.uom_id, round=False
                )

            qty = forced_qty or qty_done
            new_avg_price = 0.0
            # If the current stock is negative, we should not update the average price
            if (
                float_is_zero(product_tot_qty_available, precision_rounding=rounding)
                or product_tot_qty_available < 0
                or float_is_zero(
                    product_tot_qty_available + move.product_qty,
                    precision_rounding=rounding,
                )
                or float_is_zero(
                    product_tot_qty_available + qty, precision_rounding=rounding
                )
            ):
                new_avg_price = move.price_subtotal / qty_done
            else:
                # Get the average price
                amount_unit = avg_price_update.get(
                    (move.operating_unit_id.id, move.product_id.id)
                ) or move.product_id.with_context(average_price=True).get_product_cost(
                    move.operating_unit_id
                )
                new_avg_price = (
                    (amount_unit * product_tot_qty_available)
                    + ((move.price_subtotal / qty_done) * qty)
                ) / (product_tot_qty_available + qty)

            tmpl_dict[move.product_id.id] += qty_done
            avg_price_update[move.operating_unit_id.id, move.product_id.id] = (
                new_avg_price
            )
            move.average_price = new_avg_price

            product_cost_id = move.product_id.product_cost_ids.filtered(
                lambda p: p.operating_unit_id == move.operating_unit_id
            )
            if product_cost_id:
                product_cost_id.write({'average_price': new_avg_price})
            else:
                move.product_id.product_cost_ids.create(
                    {
                        'currency_id': move.currency_id.id,
                        'product_tmpl_id': move.product_id.product_tmpl_id.id,
                        'operating_unit_id': move.operating_unit_id.id,
                        'average_price': new_avg_price,
                    }
                )

    @api.model
    def _run_fifo(self, move, quantity=None):
        move.ensure_one()

        valued_move_lines = move.move_line_ids.filtered(
            lambda ml: ml.location_id._should_be_valued()
            and not ml.location_dest_id._should_be_valued()
        )
        valued_quantity = 0
        for valued_move_line in valued_move_lines:
            if valued_move_line.product_uom_id != move.product_id.uom_id:
                valued_quantity += valued_move_line.product_uom_id._compute_quantity(
                    valued_move_line.qty_done, move.product_id.uom_id, round=False
                )
            else:
                valued_quantity += valued_move_line.qty_done

        qty_to_take_on_candidates = quantity or valued_quantity
        new_standard_price = 0
        tmp_value = 0
        candidates = self.env['stock.move']
        if not move.location_id.return_location:
            candidates = move.product_id.with_context(
                operating_unit_id=move.operating_unit_id.id, date=move.date
            )._get_fifo_candidates_in_move()

        for candidate in candidates:
            new_standard_price = candidate.price_unit
            if candidate.product_uom != candidate.product_id.uom_id:
                new_standard_price = candidate.product_uom._compute_price(
                    new_standard_price, candidate.product_id.uom_id
                )

            qty_taken_on_candidate = qty_to_take_on_candidates
            if candidate.remaining_qty <= qty_taken_on_candidate:
                qty_taken_on_candidate = candidate.remaining_qty

            candidate_price_unit = candidate.remaining_value / candidate.remaining_qty
            value_taken_on_candidate = qty_taken_on_candidate * candidate_price_unit
            candidate_vals = {
                'remaining_qty': candidate.remaining_qty - qty_taken_on_candidate,
                'remaining_value': candidate.remaining_value - value_taken_on_candidate,
            }
            candidate.write(candidate_vals)

            qty_to_take_on_candidates -= qty_taken_on_candidate
            tmp_value += value_taken_on_candidate
            if qty_to_take_on_candidates == 0:
                break

        if new_standard_price and move.product_id.cost_method == 'fifo':
            move.product_id.sudo().with_context(
                force_company=move.company_id.id
            ).standard_price = new_standard_price

        if qty_to_take_on_candidates == 0:
            move.write(
                {
                    'value': tmp_value if not quantity else move.value or tmp_value,
                    'price_unit': tmp_value / (move.product_uom_qty or quantity),
                }
            )
        elif qty_to_take_on_candidates > 0:
            move_price_unit = move.price_unit
            if move.product_uom != move.product_id.uom_id:
                move_price_unit = move.product_uom._compute_price(
                    move_price_unit, move.product_id.uom_id
                )
            last_fifo_price = (
                new_standard_price
                or move_price_unit
                or move.product_id.get_product_cost(move.operating_unit_id)
            )

            price_unit = last_fifo_price
            if move.product_uom != move.product_id.uom_id:
                price_unit = move.product_id.uom_id._compute_price(
                    price_unit, move.product_uom
                )

            negative_stock_value = last_fifo_price * -qty_to_take_on_candidates
            tmp_value += abs(negative_stock_value)
            vals = {
                'remaining_qty': move.remaining_qty + -qty_to_take_on_candidates,
                'remaining_value': move.remaining_value + negative_stock_value,
                'value': tmp_value,
                'price_unit': price_unit,
            }
            move.write(vals)
        return tmp_value

    def _in_valuation(self, quantity):
        valued_move_lines = self.move_line_ids.filtered(
            lambda ml: not ml.location_id._should_be_valued()
            and ml.location_dest_id._should_be_valued()
        )
        valued_quantity = 0
        for valued_move_line in valued_move_lines:
            if valued_move_line.product_uom_id != self.product_id.uom_id:
                valued_quantity += valued_move_line.product_uom_id._compute_quantity(
                    valued_move_line.qty_done, self.product_id.uom_id, round=False
                )
            else:
                valued_quantity += valued_move_line.qty_done

        value = self.price_subtotal
        remaining_value = value
        remaining_qty = valued_quantity
        if quantity:
            remaining_value += self.remaining_value
            remaining_qty = self.remaining_qty + quantity

        value_to_return = value if quantity is None or not self.value else self.value
        vals = {
            'value': value_to_return,
            'remaining_value': remaining_value,
            'remaining_qty': remaining_qty,
        }

        if self.product_id.cost_method == 'standard':
            value = self.product_id.get_product_cost(self.operating_unit_id) * (
                quantity or valued_quantity
            )
            value_to_return = (
                value if quantity is None or not self.value else self.value
            )
            vals['value'] = value_to_return

        self.write(vals)

        if self.location_id.usage == 'supplier':
            self._set_vendor_pricelist()
            self._set_product_cost()

        return value_to_return

    def _out_valuation(self, quantity):
        if self.product_id.cost_method == 'fifo':
            value_to_return = self.env['stock.move']._run_fifo(self, quantity=quantity)
        else:
            valued_move_lines = self.move_line_ids.filtered(
                lambda ml: ml.location_id._should_be_valued()
                and not ml.location_dest_id._should_be_valued()
            )
            valued_quantity = 0
            for line in valued_move_lines:
                qty_done = line.qty_done
                if line.product_uom_id != self.product_id.uom_id:
                    qty_done += line.product_uom_id._compute_quantity(
                        qty_done, self.product_id.uom_id, round=False
                    )
                valued_quantity += qty_done

            valued_price = 0
            if self.product_id.cost_method == 'standard':
                valued_price = self.product_id.get_product_cost(self.operating_unit_id)
            elif self.product_id.cost_method == 'average':
                valued_price = self.product_id.with_context(
                    average_price=True
                ).get_product_cost(self.operating_unit_id)

            qty = valued_quantity if quantity is None else quantity
            value = float_round(
                valued_price * qty,
                precision_rounding=self.company_id.currency_id.rounding,
            )
            value_to_return = value if quantity is None else self.value + value
            self.value = value_to_return

        return value_to_return

    def _dropship_valuation(self, quantity):
        curr_rounding = self.company_id.currency_id.rounding
        if self.product_id.cost_method in ['fifo']:
            price_unit = self._get_price_unit()
            self.product_id.standard_price = price_unit
        else:
            price_unit = self.product_id.get_product_cost(self.operating_unit_id)
        value = float_round(
            self.product_qty * price_unit, precision_rounding=curr_rounding
        )
        value_to_return = value if self._is_dropshipped() else -value

        self.write(
            {
                'value': value_to_return,
                'price_unit': price_unit if self._is_dropshipped() else -price_unit,
            }
        )
        return value_to_return

    @api.multi
    def _is_in_transit(self):
        self.ensure_one()
        return (
            self.location_id.usage == 'transit'
            and self.location_dest_id.usage == 'internal'
        )

    @api.multi
    def _is_out_transit(self):
        self.ensure_one()
        return (
            self.location_id.usage == 'internal'
            and self.location_dest_id.usage == 'transit'
        )

    @api.multi
    def _set_main_product_cost(self):
        self.ensure_one()
        main_operating_unit_id = self.env.ref('operating_unit.main_operating_unit').id
        product_cost = self.with_context(
            operating_unit_id=main_operating_unit_id
        )._set_product_cost()
        product_cost.average_price = self.product_id.with_context(
            average_price=True
        ).get_product_cost(self.operating_unit_id)

    def _run_valuation(self, quantity=None):
        self.ensure_one()
        if self.price_total == 0:
            return 0

        value_to_return = 0
        if quantity and self.product_uom != self.product_id.uom_id:
            quantity = self.product_uom._compute_quantity(
                quantity, self.product_id.uom_id, round=False
            )
        if self._is_in():
            value_to_return = self._in_valuation(quantity)
        elif self._is_out():
            value_to_return = self._out_valuation(quantity)
        elif self._is_dropshipped() or self._is_dropshipped_returned():
            value_to_return = self._dropship_valuation(quantity)

        if self._is_out_transit():
            self._set_main_product_cost()

        return value_to_return

    def _get_new_picking_values(self):
        values = super()._get_new_picking_values()
        sale_line = self.sale_line_id
        if sale_line:
            values.update(
                {
                    'operating_unit_id': sale_line.operating_unit_id.id,
                }
            )
        return values

    @api.multi
    def _get_owner_id(self):
        self.ensure_one()
        picking_id = self.picking_id
        owner_id = False
        if self.picking_code == 'incoming':
            if picking_id.purchase_id:
                owner_id = picking_id.owner_id or picking_id.purchase_id.partner_id
            elif self.origin_returned_move_id.move_line_ids:
                owner_id = self.origin_returned_move_id.move_line_ids[0].owner_id
            else:
                owner_id = picking_id.owner_id or picking_id.partner_id
        elif self.picking_code == 'outgoing':
            if picking_id.is_vendor_return or picking_id.sale_id:
                owner_id = picking_id.partner_id
            else:
                if self.origin_returned_move_id.move_line_ids:
                    owner_id = self.origin_returned_move_id.move_line_ids[0].owner_id
                else:
                    self = self.with_context(
                        operating_unit_id=self.operating_unit_id.id
                    )
                    seller_id = self.product_id._select_seller()
                    owner_id = seller_id.name

                if not owner_id:
                    raise ValidationError(
                        _(
                            'Supplier not found for this product `%s`\n'
                            'on this Operating Unit `%s`'
                            % (
                                self.product_id.display_name,
                                self.operating_unit_id.name,
                            )
                        )
                    )
        return owner_id

    def _update_reserved_quantity(
        self,
        need,
        available_quantity,
        location_id,
        lot_id=None,
        package_id=None,
        owner_id=None,
        strict=True,
    ):
        if not owner_id and self.picking_code == 'internal' and self.operating_unit_id:
            seller = self.product_id.with_context(
                operating_unit_id=self.operating_unit_id.id
            )._select_seller()
            owner_id = seller and seller.name or False
            if not owner_id:
                raise ValidationError(
                    _(
                        'Supplier not found for this product `%s`\n'
                        'on this Operating Unit `%s`'
                        % (self.product_id.display_name, self.operating_unit_id.name)
                    )
                )

        if not lot_id:
            lot_id = self.env['stock.production.lot']
        if not package_id:
            package_id = self.env['stock.quant.package']
        if not owner_id:
            owner_id = self.env['res.partner']

        taken_quantity = min(available_quantity, need)

        if not strict:
            taken_quantity_move_uom = self.product_id.uom_id._compute_quantity(
                taken_quantity, self.product_uom, rounding_method='DOWN'
            )
            taken_quantity = self.product_uom._compute_quantity(
                taken_quantity_move_uom,
                self.product_id.uom_id,
                rounding_method='HALF-UP',
            )

        quants = []
        rounding = self.env['decimal.precision'].precision_get(
            'Product Unit of Measure'
        )

        if self.product_id.tracking == 'serial':
            if (
                float_compare(
                    taken_quantity, int(taken_quantity), precision_digits=rounding
                )
                != 0
            ):
                taken_quantity = 0

        try:
            if not float_is_zero(
                taken_quantity, precision_rounding=self.product_id.uom_id.rounding
            ):
                quants = self.env['stock.quant']._update_reserved_quantity(
                    self.product_id,
                    location_id,
                    taken_quantity,
                    lot_id=lot_id,
                    package_id=package_id,
                    owner_id=owner_id,
                    strict=strict,
                )
        except UserError:
            taken_quantity = 0

        # Find a candidate move line to update or create a new one.
        for reserved_quant, quantity in quants:
            to_update = self.move_line_ids.filtered(
                lambda m: m.product_id.tracking != 'serial'
                and m.location_id.id == reserved_quant.location_id.id
                and m.lot_id.id == reserved_quant.lot_id.id
                and m.package_id.id == reserved_quant.package_id.id
                and m.owner_id.id_pkp == reserved_quant.id_pkp
            )
            if to_update:
                move_line = to_update[0]
                uom_quantity = self.product_id.uom_id._compute_quantity(
                    quantity, move_line.product_uom_id, rounding_method='HALF-UP'
                )
                uom_quantity = float_round(uom_quantity, precision_digits=rounding)
                uom_quantity_back_to_product_uom = (
                    move_line.product_uom_id._compute_quantity(
                        uom_quantity, self.product_id.uom_id, rounding_method='HALF-UP'
                    )
                )
            if (
                to_update
                and float_compare(
                    quantity,
                    uom_quantity_back_to_product_uom,
                    precision_digits=rounding,
                )
                == 0
            ):
                to_update[0].with_context(
                    bypass_reservation_update=True
                ).product_uom_qty += uom_quantity
            else:
                if self.product_id.tracking == 'serial':
                    for i in range(0, int(quantity)):
                        self.env['stock.move.line'].create(
                            self._prepare_move_line_vals(
                                quantity=1, reserved_quant=reserved_quant
                            )
                        )
                else:
                    self.env['stock.move.line'].create(
                        self._prepare_move_line_vals(
                            quantity=quantity, reserved_quant=reserved_quant
                        )
                    )

        return taken_quantity

    @api.multi
    def _transform_uom_qty(self):
        rounding = self.env['decimal.precision'].precision_get(
            'Product Unit of Measure'
        )
        for move in self:
            if move.state in ['cancel', 'done'] or not move.move_line_ids:
                continue

            for line in move.move_line_ids:
                if (
                    line.product_uom_id != move.product_uom
                    and move.product_uom != move.product_id.uom_id
                ):
                    quantity = line.product_uom_id._compute_quantity(
                        line.product_uom_qty, move.product_uom, round=True
                    )
                    quantity_back_to_uom = move.product_uom._compute_quantity(
                        quantity, line.product_uom_id, round=False
                    )
                    if (
                        float_compare(
                            line.product_uom_qty,
                            quantity_back_to_uom,
                            precision_digits=rounding,
                        )
                        == 0
                    ):
                        line.with_context(bypass_reservation_update=True).write(
                            {
                                'product_uom_qty': quantity,
                                'product_uom_id': move.product_uom.id,
                            }
                        )

    def _action_assign(self):
        res = super()._action_assign()
        self._transform_uom_qty()
        return res

    def _prepare_move_line_vals(self, quantity=None, reserved_quant=None):
        self.ensure_one()
        vals = super()._prepare_move_line_vals(quantity, reserved_quant)
        if not vals.get('owner_id'):
            owner_id = self._get_owner_id()
            if owner_id:
                vals['owner_id'] = owner_id.id
            else:
                seller = self.product_id.with_context(
                    operating_unit_id=self.operating_unit_id.id
                )._select_seller()
                vals['owner_id'] = seller.name.id

        if self._context.get('raw_query'):
            vals['product_uom_qty'] = 0
            vals['product_qty'] = 0
            vals['date'] = fields.Datetime.now()
            vals['state'] = self.state
            vals['reference'] = self.reference
        return vals

    def _account_entry_move(self):
        if self.price_total == 0.0:
            return
        self = self.with_context(default_operating_unit_id=self.operating_unit_id.id)
        return super(StockMove, self)._account_entry_move()

    def _recompute_state(self):
        vals_to_write = []
        vals_lines_to_write = []
        for move in self:
            if move.state in ('cancel', 'done', 'draft'):
                continue

            if move.reserved_availability == move.product_uom_qty:
                state = 'assigned'
            elif (
                move.reserved_availability
                and move.reserved_availability <= move.product_uom_qty
            ):
                state = 'partially_available'
            else:
                if move.procure_method == 'make_to_order' and not move.move_orig_ids:
                    state = 'waiting'
                elif move.move_orig_ids and not all(
                    orig.state in ('done', 'cancel') for orig in move.move_orig_ids
                ):
                    state = 'waiting'
                else:
                    state = 'confirmed'

            vals_to_write.append({'id': move.id, 'state': state})
            for line in move.move_line_ids:
                vals_lines_to_write.append({'id': line.id, 'state': state})

        if vals_to_write:
            self._write_raw(vals_to_write)

        if vals_lines_to_write:
            self.env['stock.move.line']._write_raw(vals_lines_to_write)

    def _set_quantity_done(self, qty):
        product_uom_qty = qty
        if self.product_uom != self.product_id.uom_id:
            qty = self.product_uom._compute_quantity(
                qty, self.product_id.uom_id, round=False
            )

        rounding = self.product_id.uom_id.rounding

        for ml in self.move_line_ids:
            product_uom_qty = ml.product_uom_qty
            qty_done = ml.qty_done
            if ml.product_uom_id != ml.product_id.uom_id:
                product_uom_qty = ml.product_uom_id._compute_quantity(
                    product_uom_qty, ml.product_id.uom_id, round=False
                )
                qty_done = ml.product_uom_id._compute_quantity(
                    qty_done, ml.product_id.uom_id, round=False
                )

            ml_qty = product_uom_qty - qty_done

            taken_qty = min(qty, ml_qty)
            taken_qty = float_round(taken_qty, precision_rounding=rounding)

            qty -= taken_qty
            if ml.product_uom_id != ml.product_id.uom_id:
                taken_qty = ml.product_id.uom_id._compute_quantity(
                    taken_qty, ml.product_uom_id, round=False
                )
            ml.qty_done += taken_qty

            if float_compare(qty, 0.0, precision_rounding=rounding) <= 0:
                break

        if self.product_uom != self.product_id.uom_id:
            qty = self.product_id.uom_id._compute_quantity(
                qty, self.product_uom, round=False
            )

        if float_compare(qty, 0.0, precision_rounding=self.product_uom.rounding):
            if self.picking_code == 'internal' and self.move_line_ids:
                self.move_line_ids.unlink()
                self._action_confirm()
                self._action_assign()
                self._set_quantity_done(qty)
            else:
                vals = self._prepare_move_line_vals(quantity=0)
                self.env['stock.move.line'].create(dict(vals, qty_done=qty))

    @api.multi
    def _recompute_journal(self):
        if not self:
            return False

        AccountMoveLine = self.env['account.move.line']
        query = """
            SELECT json_agg(
                    json_build_object(
                        'id', aml.id,
                        %s, sm.value,
                        'quantity',
                            CASE
                                WHEN pt.code = 'outgoing'
                                THEN -sm.product_qty
                                ELSE sm.product_qty
                            END
                        )
                    )
            FROM stock_move sm
            JOIN stock_picking sp ON sp.id = sm.picking_id
            JOIN stock_picking_type pt ON pt.id = sp.picking_type_id
            JOIN account_move am on am.stock_move_id = sm.id
            JOIN account_move_line aml on aml.move_id = am.id
            WHERE sm.id in %s
            """
        query_debit = query + ' AND aml.debit > 0'
        query_credit = query + ' AND aml.credit > 0'
        params = tuple(self.ids)

        self._cr.execute(query_debit, ('debit', params))
        res_debit = self._cr.fetchone()

        self._cr.execute(query_credit, ('credit', params))
        res_credit = self._cr.fetchone()

        if res_debit and res_debit[0]:
            AccountMoveLine._write_raw(res_debit[0])

        if res_credit and res_credit[0]:
            AccountMoveLine._write_raw(res_credit[0])

        return True

    @api.multi
    def _recompute_in_valuation(self, product_id):
        if not self or not product_id:
            return False

        all_out_moves = self.search(
            [
                ('product_id', '=', product_id),
                ('location_id.usage', '=', 'internal'),
                ('location_dest_id.usage', 'in', ['customer', 'supplier']),
                ('state', '=', 'done'),
            ],
            order='date ASC',
        )
        current_qty_available = 0.0
        used_out_moves = self.env['stock.move']
        moves_data = []

        for move in self:
            out_moves = all_out_moves.filtered(lambda m: m.date <= move.date)
            if out_moves:
                out_moves -= used_out_moves
                current_qty_available -= sum(out_moves.mapped('product_qty'))
                used_out_moves += out_moves

            qty_done = move.quantity_done
            if move.product_uom != move.product_id.uom_id:
                qty_done = move.product_uom._compute_quantity(
                    qty_done, move.product_id.uom_id, round=False
                )

            average_price = move.price_subtotal / qty_done
            if current_qty_available > 0.0:
                amount_unit = move.product_id.with_context(
                    average_price=True
                ).get_product_cost(move.operating_unit_id)

                average_price = (
                    (amount_unit * current_qty_available) + (move.price_subtotal)
                ) / (current_qty_available + qty_done)

            current_qty_available += qty_done
            moves_data.append(
                {
                    'id': move.id,
                    'average_price': average_price,
                    'value': abs(move.price_subtotal),
                }
            )

            product_cost = move.product_id.product_cost_ids.filtered(
                lambda p: p.operating_unit_id == move.operating_unit_id
            )
            if product_cost:
                product_cost.average_price = average_price
            else:
                product_cost = (
                    self.env['product.cost']
                    .sudo()
                    .create(
                        {
                            'currency_id': move.currency_id.id,
                            'product_tmpl_id': move.product_id.product_tmpl_id.id,
                            'operating_unit_id': move.operating_unit_id.id,
                            'average_price': average_price,
                        }
                    )
                )

        if moves_data:
            self._write_raw(moves_data)

        self._recompute_journal()

    @api.multi
    def _recompute_out_valuation(self, product_id):
        if not self or not product_id:
            return False

        moves_data = []
        all_in_moves = self.search(
            [
                ('product_id', '=', product_id),
                ('picking_code', '=', 'incoming'),
                ('state', '=', 'done'),
            ],
            order='date DESC',
        )
        for move in self:
            value = 0.0
            in_move = all_in_moves.filtered(lambda m: m.date <= move.date)
            in_move = in_move and in_move[0] or False
            if in_move:
                qty_done = move.quantity_done
                if move.product_uom != move.product_id.uom_id:
                    qty_done = move.product_uom._compute_quantity(
                        move.quantity_done, move.product_id.uom_id, round=False
                    )
                value = in_move.average_price * qty_done
            else:
                value = abs(move.price_subtotal) / 111 * 100

            moves_data.append({'id': move.id, 'value': value})

        if moves_data:
            self._write_raw(moves_data)

        self._recompute_journal()

    @api.multi
    def _recompute_out_valuation_raw(self, product_id):
        if not self or not product_id:
            return False

        query = """
            SELECT sm.date, sm.average_price
            FROM stock_move sm
            JOIN stock_picking sp on sp.id = sm.picking_id
            JOIN stock_picking_type spt on spt.id = sp.picking_type_id
            WHERE
                spt.code = 'incoming' AND
                sm.state = 'done' AND
                sm.average_price > 0 AND
                sm.product_id = %s
            ORDER BY sm.date DESC
            """
        self._cr.execute(query, (product_id,))
        result = self._cr.fetchall()

        moves_data = []
        for move in self:
            value = 0.0
            for res in result:
                if res[0] <= move.date:
                    qty_done = move.quantity_done
                    if move.product_uom != move.product_id.uom_id:
                        qty_done = move.product_uom._compute_quantity(
                            move.quantity_done, move.product_id.uom_id, round=False
                        )
                    value = res[1] * qty_done
                    break

            value = abs(move.price_subtotal) / 111 * 100 if value == 0.0 else value
            moves_data.append({'id': move.id, 'value': value})

        if moves_data:
            self._write_raw(moves_data)

        self._recompute_journal()
