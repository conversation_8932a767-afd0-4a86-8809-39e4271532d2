# -*- coding: utf-8 -*-
# Copyright 2022 Altech Omega Andalan PT. - <PERSON>, <PERSON><PERSON>
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).

import base64
import json
import logging
from datetime import date, timedelta

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError, Warning
from odoo.tools import float_compare, float_is_zero

import odoo.addons.decimal_precision as dp

_logger = logging.getLogger(__name__)


class AccountInvoice(models.Model):
    _inherit = 'account.invoice'

    @api.one
    @api.depends('picking_ids.amount_untaxed', 'picking_ids.amount_discount')
    def _compute_amount(self):
        if self.type in ['out_invoice', 'out_refund']:
            return super()._compute_amount()

        round_curr = self.currency_id.round

        self.amount_untaxed = sum(self.picking_ids.mapped('amount_untaxed'))
        self.amount_discount = sum(self.picking_ids.mapped('amount_discount'))
        self.amount_tax = sum(
            round_curr(line.amount_total) for line in self.tax_line_ids
        )
        self.amount_total = self.amount_untaxed + self.amount_tax - self.amount_discount

        amount_total_company_signed = self.amount_total
        amount_untaxed_signed = self.amount_untaxed

        if (
            self.currency_id
            and self.company_id
            and self.currency_id != self.company_id.currency_id
        ):
            currency_id = self.currency_id.with_context(date=self.date_invoice)
            amount_total_company_signed = currency_id.compute(
                self.amount_total, self.company_id.currency_id
            )
            amount_untaxed_signed = currency_id.compute(
                self.amount_untaxed, self.company_id.currency_id
            )

        sign = self.type in ['in_refund', 'out_refund'] and -1 or 1
        self.amount_total_company_signed = amount_total_company_signed * sign
        self.amount_total_signed = self.amount_total * sign
        self.amount_untaxed_signed = amount_untaxed_signed * sign

    @api.depends('tax_line_ids')
    def _get_tax_info_JSON(self):
        for inv in self:
            inv.tax_widget = json.dumps(False)
            if inv.tax_line_ids:
                info = {
                    'title': _('Less Payment'),
                    'outstanding': False,
                    'content': inv._get_taxes_vals(),
                }
                inv.tax_widget = json.dumps(info)

    @api.multi
    @api.depends('partner_id', 'operating_unit_id')
    def _compute_domain_picking_ids(self):
        domain = []
        picking_ids = []
        if not self.partner_id or not self.operating_unit_id:
            self.domain_picking_ids = json.dumps([('id', 'in', [])])
            return

        if self.type == 'in_invoice':
            if self.partner_id.id_pkp:
                efaktur_id = self.env['upload.vendor.faktur'].search(
                    [
                        ('partner_id', 'child_of', self.partner_id.id),
                        ('state', 'in', ('on_hold', 'valid', 'done')),
                    ]
                )
                picking_ids = efaktur_id.mapped('picking_faktur_ids').filtered(
                    lambda p: p.operating_unit_id == self.operating_unit_id
                )
                if hasattr(self, '_origin') and self._origin:
                    picking_ids = picking_ids.filtered(
                        lambda p: p.invoice_status == 'to invoice'
                        or self._origin.id in p.invoice_ids.ids
                    )
                else:
                    picking_ids = picking_ids.filtered(
                        lambda p: p.invoice_status == 'to invoice'
                    )
                self.domain_picking_ids = json.dumps([('id', 'in', picking_ids.ids)])
                return
            else:
                domain.extend(
                    [
                        ('picking_type_code', '=', 'incoming'),
                        ('amount_total', '>', 0.0),
                        ('sale_id', '=', False),
                    ]
                )
        elif self.type == 'in_refund':
            domain.extend(
                [
                    ('picking_type_code', '=', 'outgoing'),
                    ('picking_type_id.is_vendor_return', '=', True),
                ]
            )
        elif self.type == 'out_refund':
            domain.extend(
                [
                    ('picking_type_code', '=', 'incoming'),
                    ('amount_total', '>', 0.0),
                    ('sale_id', '!=', False),
                ]
            )

        domain.extend(
            [
                ('partner_id', 'child_of', self.partner_id.id),
                ('operating_unit_id', '=', self.operating_unit_id.id),
                ('state', '=', 'done'),
            ]
        )
        if hasattr(self, '_origin') and self._origin:
            domain.extend(
                [
                    '|',
                    ('invoice_status', '=', 'to invoice'),
                    ('invoice_ids', '=', self._origin.id),
                ]
            )
        else:
            domain.extend([('invoice_status', '=', 'to invoice')])

        picking_ids = self.env['stock.picking'].search(domain).ids
        self.domain_picking_ids = json.dumps([('id', 'in', picking_ids)])

    operating_unit_id = fields.Many2one(index=True)
    domain_picking_ids = fields.Char(
        string='Domain for Picking',
        compute='_compute_domain_picking_ids',
    )
    selected_picking_ids = fields.Many2many(
        'stock.picking',
        relation='selected_picking_invoice_rel',
        string='Add Receive',
    )
    selected_return_ids = fields.Many2many(
        'stock.picking',
        relation='selected_return_invoice_rel',
        string='Add Return',
    )
    amount_discount = fields.Monetary(
        string='Discount',
        store=True,
        readonly=True,
        compute='_compute_amount',
        currency_field='currency_id',
        track_visibility='onchange',
    )
    efaktur_sequence_id = fields.Many2one(
        'efaktur.sequence',
        string='Efaktur Sequence',
        copy=False,
    )
    tax_number = fields.Char(string='Tax Number', copy=False)
    attachment_id = fields.Many2one('ir.attachment', readonly=True, copy=False)
    is_mass_payment = fields.Boolean(string='Is Mass Payment')
    invoice_fully_paid = fields.Boolean(string='Payment Fulfilled')
    residual_new = fields.Float(string='Amaount Due New')
    tax_widget = fields.Text(compute='_get_tax_info_JSON')
    count_print_account_invoice = fields.Integer(default=0, copy=False)
    count_without_payment = fields.Integer(default=0, copy=False)

    @api.multi
    def _get_taxes_vals(self):
        tax_vals = []
        if not self.tax_line_ids:
            return tax_vals

        for tax in self.tax_line_ids:
            tax_vals.append(
                {
                    'name': tax.name,
                    'amount': tax.amount,
                    'position': tax.currency_id.position,
                    'currency': tax.currency_id.symbol,
                    'digits': tax.currency_id.decimal_places,
                }
            )

        return tax_vals

    @api.onchange('is_mass_payment')
    def get_state_paid(self):
        for inv in self:
            if inv.is_mass_payment:
                self.write({'state': 'paid'})
            if inv.is_mass_payment and inv.state == 'open':
                self.write({'state': 'paid'})

    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        if (
            self._context.get('from_mass_payment')
            and self._context.get('partner_id')
            and self._context.get('partner_type')
            and self._context.get('company_id')
        ):
            args = args or []
            args.append(('company_id', '=', self._context.get('company_id')))
            args.append(('partner_id', 'child_of', self._context.get('partner_id')))
            args.append(('state', '=', 'open'))
            if self._context.get('partner_type') == 'customer':
                args.append(('type', '=', 'out_invoice'))
            else:
                args.append(('type', '=', 'in_invoice'))
            if self._context.get('no_of_day'):
                to_date = date.today() - timedelta(
                    days=int(self._context.get('no_of_day'))
                )
                args.append(('date_invoice', '<=', to_date))
            recs = self.browse()
            if name:
                recs = self.search([('number', '=', name)] + args, limit=limit)
            if not recs:
                recs = self.search([('name', operator, name)] + args, limit=limit)
            return recs.name_get()
        return super().name_search(name, args, operator, limit)

    @api.model
    def _get_outstanding_info_JSON(self):
        self.outstanding_credits_debits_widget = json.dumps(False)
        if self.state == 'open':
            domain = [
                ('account_id', '=', self.account_id.id),
                (
                    'partner_id',
                    '=',
                    self.env['res.partner']
                    ._find_accounting_partner(self.partner_id)
                    .id,
                ),
                ('reconciled', '=', False),
                '|',
                '&',
                ('amount_residual_currency', '!=', 0.0),
                ('currency_id', '!=', None),
                '&',
                ('amount_residual_currency', '=', 0.0),
                '&',
                ('currency_id', '=', None),
                ('amount_residual', '!=', 0.0),
            ]
            if self.type in ('out_invoice', 'in_refund'):
                domain.extend([('credit', '>', 0), ('debit', '=', 0)])
                type_payment = _('Outstanding credits')
            else:
                domain.extend([('credit', '=', 0), ('debit', '>', 0)])
                type_payment = _('Outstanding debits')
            info = {
                'title': '',
                'outstanding': True,
                'content': [],
                'invoice_id': self.id,
            }
            lines = self.env['account.move.line'].search(domain)
            currency_id = self.currency_id
            if len(lines) != 0:
                for line in lines:
                    # get the outstanding residual value in invoice currency
                    if line.currency_id and line.currency_id == self.currency_id:
                        amount_to_show = abs(line.amount_residual_currency)
                    else:
                        amount_to_show = line.company_id.currency_id.with_context(
                            date=line.date
                        ).compute(abs(line.amount_residual), self.currency_id)
                    if float_is_zero(
                        amount_to_show, precision_rounding=self.currency_id.rounding
                    ):
                        continue
                    info['content'].append(
                        {
                            'journal_name': line.ref or line.move_id.name,
                            'amount': amount_to_show,
                            'currency': currency_id.symbol,
                            'id': line.id,
                            'position': currency_id.position,
                            'digits': [69, self.currency_id.decimal_places],
                        }
                    )
                info['title'] = type_payment
                self.outstanding_credits_debits_widget = json.dumps(info)

                self.has_outstanding = self.invoice_fully_paid is False

    @api.onchange('picking_ids', 'invoice_line_ids')
    def _onchange_invoice_line_ids(self):
        taxes_grouped = self.get_taxes_values()
        tax_lines = self.tax_line_ids.filtered('manual')
        for tax in taxes_grouped.values():
            tax_lines += tax_lines.new(tax)
        self.tax_line_ids = tax_lines

        company_id = self.env.user.company_id
        if (
            self.type == 'in_invoice'
            and company_id.additional_fee
            and self.invoice_line_ids
            and sum(self.picking_ids.mapped('amount_total'))
            >= abs(company_id.additional_fee_amount)
            and not self.tax_line_ids.filtered(
                lambda t: t.name == company_id.additional_fee_name
            )
        ):
            self.tax_line_ids = [
                (
                    0,
                    0,
                    {
                        'name': company_id.additional_fee_name,
                        'account_id': company_id.additional_fee_account_id.id,
                        'amount': company_id.additional_fee_amount,
                    },
                )
            ]

    @api.onchange('partner_id', 'operating_unit_id')
    def onchange_partner_id(self):
        self.selected_picking_ids = self.selected_return_ids = False

    @api.onchange('selected_picking_ids', 'returned_picking_ids')
    def onchange_picking_ids(self):
        if hasattr(self, '_origin') and self._origin:
            self._compute_domain_picking_ids()

    def _prepare_line_vals_from_move(self, move):
        invoice_line = self.env['account.invoice.line']
        fiscal_position = self.env['account.fiscal.position']
        account_id = invoice_line.with_context(
            {'journal_id': self.journal_id.id, 'type': 'in_invoice'}
        )._default_account()
        vals = {
            'move_line_id': move.id,
            'name': move.picking_id.name + ': ' + move.name,
            'origin': move.picking_id.origin,
            'uom_id': move.product_uom.id,
            'product_id': move.product_id.id,
            'account_id': account_id,
            'price_unit': move.price_unit,
            'quantity': move.quantity_done,
            'discount_first': move.discount_first,
            'discount_second': move.discount_second,
            'discount_third': move.discount_third,
            'discount_amount': move.discount_amount,
        }

        if move.purchase_line_id:
            fiscal_position = move.purchase_line_id.order_id.fiscal_position_id
            vals['purchase_line_id'] = move.purchase_line_id.id
            vals['account_analytic_id'] = move.purchase_line_id.account_analytic_id.id
            vals['analytic_tag_ids'] = move.purchase_line_id.analytic_tag_ids.ids

        if move.sale_line_id:
            fiscal_position = move.sale_line_id.order_id.fiscal_position_id
            vals['sale_line_ids'] = move.sale_line_id.ids
            vals['analytic_tag_ids'] = move.sale_line_id.analytic_tag_ids.ids

        vals['invoice_line_tax_ids'] = fiscal_position.map_tax(move.taxes_id).ids

        fiscal_account = invoice_line.get_invoice_line_account(
            'in_invoice',
            move.product_id,
            fiscal_position,
            self.env.user.company_id,
        )
        if fiscal_account:
            vals['account_id'] = fiscal_account.id

        return vals

    @api.onchange('selected_picking_ids')
    def onchange_selected_picking_ids(self):
        self.picking_ids = False
        self.invoice_line_ids = False
        if not self.selected_picking_ids:
            return {}

        if not self.partner_id:
            self.partner_id = self.selected_picking_ids[0].partner_id.id

        for picking_id in self.selected_picking_ids:
            if (
                float_compare(
                    picking_id.amount_total,
                    0.0,
                    precision_rounding=self.currency_id.rounding,
                )
                == -1
            ):
                raise Warning(_('You cannot make an invoice with a negative amount.'))

        invoice_line_ids = self.env['account.invoice.line']
        move_lines = self.selected_picking_ids.mapped('move_lines').filtered(
            lambda m: m.price_unit > 0
        )
        for move in move_lines:
            vals = self._prepare_line_vals_from_move(move)
            new_line = invoice_line_ids.new(vals)
            new_line._set_additional_fields(self)
            invoice_line_ids += new_line

        self.invoice_line_ids += invoice_line_ids
        self.payment_term_id = self.selected_picking_ids[0].purchase_id.payment_term_id
        self.env.context = dict(self.env.context, from_purchase_order_change=True)
        self.picking_ids = [(6, 0, self.selected_picking_ids.ids)]
        return {}

    @api.onchange('selected_return_ids')
    def onchange_selected_return_ids(self):
        self.picking_ids = False
        self.invoice_line_ids = False
        if not self.selected_return_ids:
            return {}

        if not self.partner_id:
            self.partner_id = self.selected_return_ids[0].partner_id.id

        for picking_id in self.selected_return_ids:
            if (
                float_compare(
                    picking_id.amount_total,
                    0.0,
                    precision_rounding=self.currency_id.rounding,
                )
                == -1
            ):
                raise Warning(
                    _('You cannot make an credit notes with a negative amount.')
                )

        invoice_line_ids = self.env['account.invoice.line']
        move_lines = self.selected_return_ids.mapped('move_lines').filtered(
            lambda m: m.price_unit > 0
        )
        for move in move_lines:
            vals = self._prepare_line_vals_from_move(move)
            new_line = invoice_line_ids.new(vals)
            new_line._set_additional_fields(self)
            invoice_line_ids += new_line

        self.invoice_line_ids += invoice_line_ids
        self.picking_ids = [(6, 0, self.selected_return_ids.ids)]
        return {}

    @api.onchange('invoice_line_ids')
    def _onchange_origin(self):
        self.origin = ', '.join(
            (self.selected_picking_ids or self.selected_return_ids).mapped('name')
        )

    @api.multi
    def get_taxes_values(self):
        round_curr = self.currency_id.round
        tax_grouped = {}
        if self.picking_ids and (self.selected_picking_ids or self.selected_return_ids):
            tax_vals = []
            for tax_data in self.picking_ids.mapped('tax_data'):
                try:
                    vals = json.loads(base64.b64decode(tax_data).decode('utf-8'))
                    tax_vals.append(vals)
                except Exception as e:
                    _logger.error(f'Failed to get tax data {self}, {e}')

            if not tax_vals:
                return tax_grouped

            for tax in tax_vals:
                for key, val in tax.items():
                    if key in tax_grouped:
                        tax_grouped[key]['amount'] += val['amount']
                        tax_grouped[key]['base'] += val['base']
                    else:
                        tax_grouped[key] = val.copy()
                        tax_grouped[key]['invoice_id'] = self.id
        else:
            for line in self.invoice_line_ids:
                price_unit = line._get_discounted_price_unit()
                taxes = line.invoice_line_tax_ids.compute_all(
                    price_unit,
                    self.currency_id,
                    line.quantity,
                    line.product_id,
                    self.partner_id,
                )['taxes']
                for tax in taxes:
                    val = self._prepare_tax_line_vals(line, tax)
                    key = (
                        self.env['account.tax'].browse(tax['id']).get_grouping_key(val)
                    )

                    if key not in tax_grouped:
                        tax_grouped[key] = val
                        tax_grouped[key]['base'] = round_curr(val['base'])
                    else:
                        tax_grouped[key]['amount'] += val['amount']
                        tax_grouped[key]['base'] += round_curr(val['base'])
        return tax_grouped

    @api.model
    def invoice_line_move_line_get(self):
        res = super().invoice_line_move_line_get()
        if self.amount_discount:
            if not self.company_id.discount_account_id:
                raise ValidationError(
                    _('Account for Global Discount has not been configured !')
                )

            discount_dict = {}
            account_analytic_ids = self.invoice_line_ids.mapped('account_analytic_id')
            analytic_tag_ids = self.invoice_line_ids.mapped('analytic_tag_ids')
            discount_dict.update(
                {
                    'type': 'global_discount',
                    'name': 'Global Discount',
                    'price_unit': self.amount_discount * -1,
                    'quantity': 1,
                    'price': self.amount_discount * -1,
                    'account_id': self.company_id.discount_account_id.id,
                    'account_analytic_id': account_analytic_ids
                    and account_analytic_ids[0].id
                    or False,
                    'analytic_tag_ids': [(4, at.id) for at in analytic_tag_ids],
                }
            )
            res.append(discount_dict)
        return res

    @api.multi
    def action_move_create(self):
        ctx = {'default_operating_unit_id': self.operating_unit_id.id}
        return super(AccountInvoice, self.with_context(ctx)).action_move_create()

    @api.multi
    def _check_access_report(self):
        if not self.env['ir.model.access'].check_groups(
            'account.group_account_manager'
        ):
            raise UserError(
                _('Anda Tidak Memiliki Akses, Hubungi administrator atau atasan Anda!')
            )

    @api.multi
    def _check_access_report_reprint(self):
        if self.count_print_account_invoice > 0 and not self.user_has_groups(
            'niaga_base.group_reprint_document'
        ):
            raise UserError(_('You are not allow to reprint this document.'))

    @api.multi
    def invoice_print(self):
        self.ensure_one()
        self._check_access_report()
        self._check_access_report_reprint()
        self.sent = True
        self.count_print_account_invoice += 1

        if self.user_has_groups('account.group_account_invoice'):
            return self.env.ref('account.account_invoices').report_action(self)
        else:
            return self.env.ref(
                'account.account_invoices_without_payment'
            ).report_action(self)

    @api.multi
    def action_account_invoices_report(self):
        for inv in self:
            inv._check_access_report_reprint()
            inv.count_print_account_invoice += 1
        return self.env.ref('account.account_invoices').report_action(self)

    @api.multi
    def report_account_invoices_pdf(self):
        self._check_access_report()
        return self.action_account_invoices_report()

    @api.multi
    def action_invoices_without_payment_report(self):
        for inv in self:
            inv._check_access_report_reprint()
            inv.count_without_payment += 1
        return inv.env.ref('account.account_invoices_without_payment').report_action(
            self
        )

    @api.multi
    def report_without_payment_pdf(self):
        self._check_access_report()
        return self.action_invoices_without_payment_report()

    @api.multi
    def action_invoice_open(self):
        self.count_without_payment = 0
        self.count_print_account_invoice = 0
        return super().action_invoice_open()

    @api.model
    def _anglo_saxon_purchase_move_lines(self, i_line, res):
        inv = i_line.invoice_id
        company_currency = inv.company_id.currency_id
        invoice_currency = inv.currency_id
        product = i_line.product_id
        if (
            not product
            or product.valuation != 'real_time'
            or product.type != 'product'
            or product.cost_method == ' average'
        ):
            return []
        # get the fiscal position
        fpos = i_line.invoice_id.fiscal_position_id
        # get the price difference account at the product
        acc = (
            product.property_account_creditor_price_difference
            or product.categ_id.property_account_creditor_price_difference_categ
        )
        acc = fpos.map_account(acc).id
        # reference_account_id is the stock input account
        reference_account_id = product.product_tmpl_id.get_product_accounts(
            fiscal_pos=fpos
        )['stock_input'].id
        diff_res = []
        for line in res:
            if (
                line.get('invl_id', 0) != i_line.id
                or reference_account_id != line['account_id']
            ):
                continue

            standard_price = product.get_product_cost(self.operating_unit_id)
            valuation_price_unit = product.uom_id._compute_price(
                standard_price, i_line.uom_id
            )
            currency_valuation_price_unit = company_currency
            if product.cost_method != 'standard' and i_line.move_line_id:
                valuation_stock_move = i_line.move_line_id
                if self.type == 'in_refund':
                    valuation_stock_move = valuation_stock_move.filtered(
                        lambda m: m._is_out()
                    )
                elif self.type == 'in_invoice':
                    valuation_stock_move = valuation_stock_move.filtered(
                        lambda m: m._is_in()
                    )

                valuation_price_unit = abs(valuation_stock_move.price_unit)

            # Put the valuation price unit in the invoice currency
            if invoice_currency != currency_valuation_price_unit:
                valuation_price_unit_invoice_currency = (
                    currency_valuation_price_unit.with_context(
                        date=inv.date_invoice
                    ).compute(valuation_price_unit, invoice_currency, round=False)
                )
            else:
                valuation_price_unit_invoice_currency = valuation_price_unit

            # Valuation price unit and i_line.price_unit in invoice currency
            if (
                not acc
                or float_compare(
                    line['price_unit'],
                    i_line.price_unit,
                    precision_rounding=invoice_currency.rounding,
                )
                != 0
                or float_compare(
                    valuation_price_unit_invoice_currency,
                    i_line.price_unit,
                    precision_rounding=invoice_currency.rounding,
                )
                == 0
            ):
                continue

            # price with discount and without tax included
            price_unit = i_line.price_unit * (1 - (i_line.discount or 0.0) / 100.0)
            tax_ids = []
            if line['tax_ids']:
                # line['tax_ids'] is like [(4, tax_id, None), (4, tax_id2, None)...]
                taxes = self.env['account.tax'].browse([x[1] for x in line['tax_ids']])
                price_unit = taxes.compute_all(
                    price_unit, currency=invoice_currency, quantity=1.0
                )['total_excluded']
                for tax in taxes:
                    tax_ids.append((4, tax.id, None))
                    for child in tax.children_tax_ids:
                        if child.type_tax_use != 'none':
                            tax_ids.append((4, child.id, None))

            price_before = line.get('price', 0.0)
            line.update(
                {
                    'price': invoice_currency.round(
                        valuation_price_unit_invoice_currency * line['quantity']
                    )
                }
            )
            diff_res.append(
                {
                    'type': 'src',
                    'name': i_line.name[:64],
                    'price_unit': invoice_currency.round(
                        price_unit - valuation_price_unit_invoice_currency
                    ),
                    'quantity': line['quantity'],
                    'price': invoice_currency.round(
                        price_before - line.get('price', 0.0)
                    ),
                    'account_id': acc,
                    'product_id': line['product_id'],
                    'uom_id': line['uom_id'],
                    'account_analytic_id': line['account_analytic_id'],
                    'tax_ids': tax_ids,
                }
            )
        return diff_res

    @api.multi
    def convert_move_lines_to_anglosaxon(self):
        inv_line_vals = []
        move_line_vals = []
        for inv in self.filtered(
            lambda inv: inv.company_id.anglo_saxon_accounting
            and inv.move_id
            and inv.state in ['open', 'paid']
        ):
            payment_lines = False
            if inv.payment_move_line_ids:
                payment_lines = inv.payment_move_line_ids
                payment_lines.remove_move_reconcile()

            inv.action_invoice_cancel()
            inv.action_invoice_draft()
            inv.action_invoice_open()

            fpos = inv.fiscal_position_id
            if inv.type in ['in_invoice', 'in_refund']:
                for move_line in inv.move_id.line_ids.filtered(
                    lambda l: l.invoice_line_id and l.product_id
                ):
                    account = move_line.product_id.product_tmpl_id.get_product_accounts(
                        fiscal_pos=fpos
                    )['stock_input']
                    vals = {'account_id': account.id}
                    move_line_vals.append(
                        dict(
                            vals, id=move_line.id, user_type_id=account.user_type_id.id
                        )
                    )
                    inv_line_vals.append(dict(vals, id=move_line.invoice_line_id.id))

            if payment_lines:
                for payment_line in payment_lines:
                    inv.assign_outstanding_credit(payment_line.id)

        if inv_line_vals:
            self.env['account.invoice.line']._write_raw(inv_line_vals)
        if move_line_vals:
            self.env['account.move.line']._write_raw(move_line_vals)

    def _cron_fix_journal(self):
        # invoices = self.search([('move_id', '!=', False)])
        for inv in self:
            move = inv.move_id
            move.state = 'draft'
            move_lines = move.line_ids.filtered(lambda l: not l.invoice_id)
            move_line_vals = []
            for inv_line in inv.invoice_line_ids:
                move_lines = move_lines.filtered(
                    lambda l: l.product_id.id == inv_line.product_id.id
                )
                price_unit = inv_line._get_anglo_saxon_price_unit()
                price = price_unit * inv_line.quantity
                for move_line in move_lines:
                    vals = {'id': move_line.id}
                    if move_line.credit > 0.0:
                        move_line.write({'credit': price})
                        vals['credit'] = price
                    elif move_line.debit > 0.0:
                        move_line.write({'debit': price})
                        vals['debit'] = price
                    move_line_vals.append(vals)
                print('======')
                print(move_lines.mapped('credit'))
                print(move_lines.mapped('debit'))
                print('======')
            if move_line_vals:
                self.env['account.move.line']._write_raw(move_line_vals)
                writed_move_lines = self.env['account.move.line'].browse(
                    [x['id'] for x in move_line_vals]
                )
                writed_move_lines._recompute_all()
            move._recompute_all()
            move.state = 'posted'


class AccountInvoiceLine(models.Model):
    _inherit = 'account.invoice.line'

    operating_unit_id = fields.Many2one(index=True)
    discount_first = fields.Float(
        string='Disc 1 (%)',
        digits=dp.get_precision('Discount'),
    )
    discount_second = fields.Float(
        string='Disc 2 (%)',
        digits=dp.get_precision('Discount'),
    )
    discount_third = fields.Float(
        string='Disc 3 (%)',
        digits=dp.get_precision('Discount'),
    )
    discount_amount = fields.Monetary(
        string='Disc Amount',
        currency_field='currency_id',
    )
    price_discount = fields.Float(
        compute='_compute_price',
        string='Discount',
        store=True,
    )
    purchase_line_id = fields.Many2one(readonly=False)
    move_line_id = fields.Many2one(
        'stock.move',
        String='Stock Move',
        ondelete='set null',
        index=True,
        readonly=False,
    )
    picking_id = fields.Many2one(
        related='move_line_id.picking_id',
        store=False,
        readonly=True,
        related_sudo=False,
    )

    def _get_discounted_price_unit(self):
        self.ensure_one()
        price_unit = self.price_unit
        if self.discount_first:
            price_unit *= 1 - self.discount_first / 100
        if self.discount_second:
            price_unit *= 1 - self.discount_second / 100
        if self.discount_third:
            price_unit *= 1 - self.discount_third / 100
        if self.discount_amount:
            price_unit -= self.discount_amount
        return price_unit

    @api.one
    @api.depends(
        'discount_first', 'discount_second', 'discount_third', 'discount_amount'
    )
    def _compute_price(self):
        prev_price_unit = self.price_unit
        price_unit = self._get_discounted_price_unit()
        self.update(
            {
                'price_unit': price_unit,
                'price_discount': prev_price_unit - price_unit,
            }
        )
        super(AccountInvoiceLine, self)._compute_price()
        self.update({'price_unit': prev_price_unit})

    def _get_anglo_saxon_price_unit(self):
        self.ensure_one()
        if not self.product_id:
            return self.price_unit
        return self.product_id._get_anglo_saxon_price_unit(
            uom=self.uom_id, operating_unit_id=self.operating_unit_id
        )


class AccountInvoiceTax(models.Model):
    _inherit = 'account.invoice.tax'
    _order = 'id ASC'
